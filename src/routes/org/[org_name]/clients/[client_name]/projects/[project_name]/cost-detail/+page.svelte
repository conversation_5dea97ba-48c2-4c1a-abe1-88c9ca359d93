<script lang="ts">
	import type {
		CostDetailBudgetItem,
		CostDetailWorkPackageData,
		CostDetailPurchaseOrderData,
	} from '$lib/schemas/cost_detail';
	import { createGenericBudgetHierarchy } from '$lib/budget_utils';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { formatCurrency } from '$lib/schemas/purchase_order';
	import type { HierarchyNode } from 'd3-hierarchy';
	import type { WbsItemWithBudgetData } from '$lib/budget_utils';
	import { SvelteSet } from 'svelte/reactivity';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { page } from '$app/state';
	import { useSidebar } from '$lib/components/ui/sidebar/context.svelte.js';

	const { data } = $props();
	const { costDetailData, allWbsItems } = data;

	// State for hiding zero values
	let hideZeros = $state(true);

	// State for expanding/collapsing nodes
	let expandedNodeIds = new SvelteSet<string>();

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}

	// Create the hierarchical structure using the budget utils
	const costHierarchy = $derived.by(() => {
		if (!allWbsItems || !costDetailData) {
			return null;
		}
		return createGenericBudgetHierarchy(allWbsItems, costDetailData);
	});

	// Type for hierarchy node with cost detail data
	type CostDetailHierarchyNode = HierarchyNode<WbsItemWithBudgetData<CostDetailBudgetItem>>;

	// Check if a node has its own values (not considering children)
	const nodeHasOwnValues = (node: CostDetailHierarchyNode): boolean => {
		// Safety check for node and node.data
		if (!node || !node.data) return false;

		// Safely access budgetData
		const budgetData = node.data?.budgetData;

		// Show if has budget amount
		if (budgetData?.budget_amount && budgetData.budget_amount > 0) return true;

		// Show if has work packages
		if (budgetData?.work_packages && budgetData.work_packages.length > 0) return true;

		// Show if has purchase orders
		if (budgetData?.purchase_orders && budgetData.purchase_orders.length > 0) return true;

		return false;
	};

	// Calculate total purchase order amount (original_amount + co_amount)
	const calculatePurchaseOrderTotal = (
		purchaseOrders: CostDetailPurchaseOrderData[],
	): number | null => {
		if (!purchaseOrders || purchaseOrders.length === 0) return null;

		const total = purchaseOrders.reduce((sum, po) => {
			const originalAmount = po.original_amount || 0;
			const coAmount = po.co_amount || 0;
			return sum + originalAmount + coAmount;
		}, 0);

		return total > 0 ? total : null;
	};

	// Type for display row
	type DisplayRow = {
		id: string;
		wbs_code: string;
		wbs_description: string;
		budget_amount: number | null;
		hierarchical_total: number | null; // Total including children from stratify
		is_hierarchical_total: boolean; // Whether this row shows a hierarchical total
		work_packages: CostDetailWorkPackageData[];
		purchase_orders: CostDetailPurchaseOrderData[];
		purchase_order_total: number | null; // Total amount of all purchase orders for this row
		indentStyle: string;
		level: number;
		hasChildren: boolean;
		nodeId: string;
		// New fields for work package rows
		is_work_package_row: boolean; // Whether this is a work package row
		work_package: CostDetailWorkPackageData | null; // Single work package for work package rows
		work_package_purchase_orders: CostDetailPurchaseOrderData[]; // Purchase orders for this specific work package
		// New fields for purchase order rows
		is_purchase_order_row: boolean; // Whether this is a purchase order row
		purchase_order: CostDetailPurchaseOrderData | null; // Single purchase order for purchase order rows
		purchase_order_amount: number | null; // Amount for this specific purchase order
	};

	// Recursive function to render hierarchy nodes with improved filtering and expand/collapse support
	function renderHierarchyRows(node: CostDetailHierarchyNode, level: number = 0): DisplayRow[] {
		// Safety check for node and node.data
		if (!node || !node.data) return [];

		const indentStyle = level > 0 ? `padding-left: ${level * 1.5}rem;` : '';
		// Safely access budgetData
		const budgetData = node.data?.budgetData;
		const workPackages = budgetData?.work_packages || [];
		const purchaseOrders = budgetData?.purchase_orders || [];
		const nodeId = node.data.wbs_library_item_id || `unknown-${level}`;
		const hasChildren = Boolean(node.children && node.children.length > 0);

		const rows: DisplayRow[] = [];

		// Check if this node is expanded (default to expanded if not in the set)
		const isExpanded = !expandedNodeIds.has(nodeId);

		// First, process children to get their rows (only if expanded)
		const childRows: DisplayRow[] = [];
		if (hasChildren && isExpanded) {
			for (const child of node.children!) {
				childRows.push(...renderHierarchyRows(child, level + 1));
			}
		}

		// Determine if this node should be shown:
		// When hideZeros is true, exclude rows where hierarchical_total equals 0
		// When hideZeros is false, show all rows
		const hasOwnValues = nodeHasOwnValues(node);
		const hasChildrenWithValues = childRows.length > 0;
		const hierarchicalTotal = node.value || 0;

		// Apply zero filtering based on hierarchical_total
		const shouldShow =
			!hideZeros || hierarchicalTotal !== 0 || hasOwnValues || hasChildrenWithValues;

		// If we should show this node, add it to the rows
		if (shouldShow) {
			// Determine if this should show hierarchical total or individual budget amount
			// Show hierarchical total for parent nodes that have children with values
			const hasChildrenWithBudgetValues =
				hasChildren &&
				childRows.some((row) => row.budget_amount !== null || row.hierarchical_total !== null);
			const isHierarchicalTotal =
				hasChildren && (hasChildrenWithBudgetValues || (node.value || 0) > 0);

			// Create the parent WBS row
			const parentRow: DisplayRow = {
				id: nodeId,
				wbs_code: budgetData?.wbs_code || node.data.code || '—',
				wbs_description: budgetData?.wbs_description || node.data.description || '—',
				budget_amount: budgetData?.budget_amount || null,
				hierarchical_total: isHierarchicalTotal ? node.value || null : null,
				is_hierarchical_total: isHierarchicalTotal,
				work_packages: workPackages,
				purchase_orders: purchaseOrders,
				purchase_order_total: calculatePurchaseOrderTotal(purchaseOrders),
				indentStyle,
				level,
				hasChildren,
				nodeId,
				is_work_package_row: false,
				work_package: null,
				work_package_purchase_orders: [],
				is_purchase_order_row: false,
				purchase_order: null,
				purchase_order_amount: null,
			};
			rows.push(parentRow);

			// Create individual work package rows if there are work packages
			if (workPackages.length > 0) {
				for (const workPackage of workPackages) {
					// Find purchase orders for this specific work package
					const workPackagePurchaseOrders = purchaseOrders.filter(
						(po) => po.work_package_id === workPackage.work_package_id,
					);

					// Only show work package row if it has multiple purchase orders or if we want to show summary
					if (workPackagePurchaseOrders.length > 1) {
						const workPackageRow: DisplayRow = {
							id: `${nodeId}-wp-${workPackage.work_package_id}`,
							wbs_code: '', // Empty for work package rows
							wbs_description: '', // Empty for work package rows
							budget_amount: null, // Empty for work package rows
							hierarchical_total: null, // Empty for work package rows
							is_hierarchical_total: false,
							work_packages: [], // Empty for work package rows
							purchase_orders: [], // Empty for work package rows
							purchase_order_total: calculatePurchaseOrderTotal(workPackagePurchaseOrders),
							indentStyle:
								level > 0 ? `padding-left: ${(level + 1) * 1.5}rem;` : 'padding-left: 1.5rem;',
							level: level + 1,
							hasChildren: false,
							nodeId: `${nodeId}-wp-${workPackage.work_package_id}`,
							is_work_package_row: true,
							work_package: workPackage,
							work_package_purchase_orders: workPackagePurchaseOrders,
							is_purchase_order_row: false,
							purchase_order: null,
							purchase_order_amount: null,
						};
						rows.push(workPackageRow);
					}

					// Create individual purchase order rows
					for (const purchaseOrder of workPackagePurchaseOrders) {
						const poAmount = (purchaseOrder.original_amount || 0) + (purchaseOrder.co_amount || 0);
						const purchaseOrderRow: DisplayRow = {
							id: `${nodeId}-wp-${workPackage.work_package_id}-po-${purchaseOrder.purchase_order_id}`,
							wbs_code: '', // Empty for purchase order rows
							wbs_description: '', // Empty for purchase order rows
							budget_amount: null, // Empty for purchase order rows
							hierarchical_total: null, // Empty for purchase order rows
							is_hierarchical_total: false,
							work_packages: [], // Empty for purchase order rows
							purchase_orders: [], // Empty for purchase order rows
							purchase_order_total: null, // Empty for purchase order rows
							indentStyle:
								level > 0 ? `padding-left: ${(level + 2) * 1.5}rem;` : 'padding-left: 3rem;',
							level: level + 2,
							hasChildren: false,
							nodeId: `${nodeId}-wp-${workPackage.work_package_id}-po-${purchaseOrder.purchase_order_id}`,
							is_work_package_row: false,
							work_package: null,
							work_package_purchase_orders: [],
							is_purchase_order_row: true,
							purchase_order: purchaseOrder,
							purchase_order_amount: poAmount > 0 ? poAmount : null,
						};
						rows.push(purchaseOrderRow);
					}

					// For single purchase order, also show work package name for context
					if (workPackagePurchaseOrders.length === 1) {
						const workPackageRow: DisplayRow = {
							id: `${nodeId}-wp-${workPackage.work_package_id}`,
							wbs_code: '', // Empty for work package rows
							wbs_description: '', // Empty for work package rows
							budget_amount: null, // Empty for work package rows
							hierarchical_total: null, // Empty for work package rows
							is_hierarchical_total: false,
							work_packages: [], // Empty for work package rows
							purchase_orders: [], // Empty for work package rows
							purchase_order_total: calculatePurchaseOrderTotal(workPackagePurchaseOrders),
							indentStyle:
								level > 0 ? `padding-left: ${(level + 1) * 1.5}rem;` : 'padding-left: 1.5rem;',
							level: level + 1,
							hasChildren: false,
							nodeId: `${nodeId}-wp-${workPackage.work_package_id}`,
							is_work_package_row: true,
							work_package: workPackage,
							work_package_purchase_orders: workPackagePurchaseOrders,
							is_purchase_order_row: false,
							purchase_order: null,
							purchase_order_amount: null,
						};
						rows.push(workPackageRow);
					}
				}
			}
		}

		// Add child rows (they've already been filtered and only included if expanded)
		rows.push(...childRows);

		return rows;
	}

	// Create display rows from hierarchy
	const displayRows = $derived.by((): DisplayRow[] => {
		if (!costHierarchy) return [];
		return renderHierarchyRows(costHierarchy as unknown as CostDetailHierarchyNode);
	});

	const sidebar = useSidebar();
</script>

<div class="px-4 py-6">
	<div class="space-y-6">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="sr-only text-3xl font-bold tracking-tight">Cost Detail</h1>
				<!-- <p class="text-muted-foreground">
					Hierarchical view of WBS codes, budget amounts, work packages, and purchase orders
				</p> -->
			</div>

			<div class="flex items-center space-x-2">
				<Checkbox id="hide-zeros" bind:checked={hideZeros} />
				<Label for="hide-zeros">Hide zero values</Label>
			</div>
		</div>

		{#if !costDetailData || costDetailData.length === 0}
			<div class="py-12 text-center">
				<p class="text-muted-foreground text-lg">No cost detail data found</p>
				<p class="text-muted-foreground mt-2 text-sm">
					Add budget items, work packages, or purchase orders to see cost details
				</p>
			</div>
		{:else if displayRows.length === 0}
			<div class="py-12 text-center">
				<p class="text-muted-foreground text-lg">No items to display</p>
				<p class="text-muted-foreground mt-2 text-sm">
					Try unchecking "Hide zero values" to see all items
				</p>
			</div>
		{:else}
			<div
				class="-mr-4 -ml-4 overflow-x-scroll tabular-nums"
				style={`max-width: ${
					sidebar.open
						? 'calc(100vw - var(--sidebar-width) - 1rem)'
						: 'calc(100vw - var(--sidebar-width-icon) - 1rem)'
				}; max-height: calc(100vh - 11.5rem); position: relative;`}
			>
				<table class="w-full table-fixed caption-bottom text-sm tabular-nums">
					<thead class="bg-muted sticky top-0 z-10 border-b">
						<tr
							class="hover:bg-muted/50 data-[state=selected]:bg-muted border-b font-bold transition-colors"
						>
							<th class="bg-muted h-12 w-48 px-4 text-left align-middle">WBS Code</th>
							<th class="bg-muted h-12 w-96 px-4 text-left align-middle">Description</th>
							<th class="bg-muted h-12 w-48 px-4 pr-4 text-right align-middle"
								>Budget Amount / Total</th
							>
							<th class="bg-muted h-12 w-48 px-4 text-left align-middle">Work Packages</th>
							<th class="bg-muted h-12 w-48 px-4 text-left align-middle">Purchase Orders</th>
							<th class="bg-muted h-12 w-48 px-4 pr-4 text-right align-middle">PO Amount</th>
						</tr>
					</thead>
					<tbody class="[&_tr:last-child]:border-0">
						{#each displayRows as row (row.id)}
							{#if row.is_purchase_order_row}
								<!-- Purchase Order Row -->
								<tr
									class="hover:bg-muted/50 data-[state=selected]:bg-muted bg-muted/5 purchase-order-row border-b transition-colors"
								>
									<td class="p-4 align-middle font-medium"></td>
									<td class="text-muted-foreground p-4 align-middle"></td>
									<td class="p-4 pr-4 text-right align-middle"></td>
									<td class="text-muted-foreground p-4 align-middle"></td>
									<td class="text-muted-foreground p-4 align-middle whitespace-normal">
										<div style={row.indentStyle}>
											{#if row.purchase_order}
												<a
													href="/org/{encodeURIComponent(
														page.params.org_name!,
													)}/clients/{encodeURIComponent(
														data.client_name,
													)}/projects/{encodeURIComponent(data.project_name)}/purchase-order/{row
														.purchase_order.purchase_order_id}/edit"
													class="text-blue-600 hover:text-blue-800 hover:underline"
												>
													{row.purchase_order.po_number}{row.purchase_order.vendor_name
														? ` (${row.purchase_order.vendor_name})`
														: ''}
												</a>
											{:else}
												—
											{/if}
										</div>
									</td>
									<td class="p-4 pr-4 text-right align-middle">
										{#if row.purchase_order_amount}
											{formatCurrency(row.purchase_order_amount, 0)}
										{:else}
											—
										{/if}
									</td>
								</tr>
							{:else if row.is_work_package_row}
								<!-- Work Package Row -->
								<tr
									class="hover:bg-muted/50 data-[state=selected]:bg-muted bg-muted/10 work-package-row border-b transition-colors"
								>
									<td class="p-4 align-middle font-medium"></td>
									<td class="text-muted-foreground p-4 align-middle"></td>
									<td class="p-4 pr-4 text-right align-middle"></td>

									<td class="text-muted-foreground p-4 align-middle whitespace-normal">
										<div style={row.indentStyle}>
											{#if row.work_package}
												<a
													href="/org/{encodeURIComponent(
														data.org_name,
													)}/clients/{encodeURIComponent(
														data.client_name,
													)}/projects/{encodeURIComponent(data.project_name)}/work-package/{row
														.work_package.work_package_id}"
													class="text-blue-600 hover:text-blue-800 hover:underline"
												>
													{#if row.work_package_purchase_orders.length > 1}
														<span class="font-medium">Total: </span>
													{/if}
													{row.work_package.name}{row.work_package.description?.length
														? ` (${row.work_package.description})`
														: ''}
												</a>
											{:else}
												—
											{/if}
										</div>
									</td>
									<td class="text-muted-foreground p-4 align-middle whitespace-normal">
										<div style={row.indentStyle}>
											{#if row.work_package_purchase_orders.length > 1}
												<span class="text-muted-foreground text-sm">
													{row.work_package_purchase_orders.length} purchase orders
												</span>
											{:else if row.work_package_purchase_orders.length === 1}
												—
											{:else}
												—
											{/if}
										</div>
									</td>
									<td class="p-4 pr-4 text-right align-middle">
										{#if row.purchase_order_total && row.work_package_purchase_orders.length > 1}
											<span class="font-medium">{formatCurrency(row.purchase_order_total, 0)}</span>
										{:else if row.purchase_order_total}
											{formatCurrency(row.purchase_order_total, 0)}
										{:else}
											—
										{/if}
									</td>
								</tr>
							{:else}
								<!-- WBS Code Row -->
								<tr
									class={[
										'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',
										row.is_hierarchical_total ? 'bg-muted/30 code-row font-medium' : 'code-row',
									]}
								>
									<td class="p-4 align-middle font-medium">
										<div class="flex items-center" style={row.indentStyle}>
											{#if row.hasChildren}
												{@const isExpanded = !expandedNodeIds.has(row.nodeId)}
												<button
													onclick={() => toggleNodeExpanded(row.nodeId)}
													aria-label={isExpanded ? 'Collapse' : 'Expand'}
													class="hover:bg-muted mr-2 rounded p-1"
												>
													{#if isExpanded}
														<CaretDownIcon class="size-4" />
													{:else}
														<CaretRightIcon class="size-4" />
													{/if}
												</button>
												<span class={row.is_hierarchical_total ? 'font-semibold' : ''}
													>{row.wbs_code}</span
												>
											{:else}
												<span class="ml-8">{row.wbs_code}</span>
											{/if}
										</div>
									</td>
									<td
										class="p-4 align-middle {row.is_hierarchical_total
											? 'text-foreground'
											: 'text-muted-foreground'}"
									>
										<p
											class="w-full text-wrap {row.is_hierarchical_total ? 'font-medium' : ''}"
											style={row.indentStyle}
										>
											{row.wbs_description}
										</p>
									</td>
									<td class="p-4 pr-4 text-right align-middle">
										<div class="flex flex-col">
											{#if row.hierarchical_total}
												<span class="font-bold">
													{formatCurrency(row.hierarchical_total, 0)}
												</span>
											{/if}
											{#if row.budget_amount}
												<span class="text-muted-foreground pt-1 text-sm">
													{formatCurrency(row.budget_amount, 0)}
												</span>
											{/if}
											{#if !row.hierarchical_total && !row.budget_amount}
												—
											{/if}
										</div>
									</td>
									<td class="p-4 pr-4 text-right align-middle"></td>
									<td class="text-muted-foreground p-4 align-middle">
										{#if !row.work_packages}
											—
										{/if}
									</td>
									<td class="text-muted-foreground p-4 align-middle">
										{#if !row.purchase_orders}
											—
										{/if}
									</td>
								</tr>
							{/if}
						{/each}
					</tbody>
				</table>
			</div>
		{/if}
	</div>
</div>
